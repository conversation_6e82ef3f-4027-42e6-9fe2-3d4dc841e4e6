import warnings
import os
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import json
import time
import numpy as np
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont

warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Installing...")
    os.system("pip install easyocr")
    import easyocr
    EASYOCR_AVAILABLE = True

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Installing...")
    os.system("pip install cnocr")
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Installing...")
    os.system("pip install pytesseract")
    import pytesseract
    TESSERACT_AVAILABLE = True

# 移除MMOCR依赖，使用更简单的OCR引擎组合
MMOCR_AVAILABLE = False

def init_ocr_engines():
    """
    共享的OCR引擎初始化函数
    """
    ocr_engines = {}

    # EasyOCR引擎 - 支持中英文
    if EASYOCR_AVAILABLE:
        try:
            ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
            print("✓ EasyOCR引擎初始化成功")
        except Exception as e:
            print(f"✗ EasyOCR引擎初始化失败: {e}")

    # CnOCR引擎 - 高精度中文识别
    if CNOCR_AVAILABLE:
        try:
            ocr_engines['cnocr'] = CnOcr(
                rec_model_name='densenet_lite_136-gru',
                det_model_name='db_resnet18',
                use_gpu=torch.cuda.is_available()
            )
            print("✓ CnOCR引擎初始化成功")
        except Exception as e:
            print(f"✗ CnOCR引擎初始化失败: {e}")

    # Tesseract引擎
    if TESSERACT_AVAILABLE:
        try:
            pytesseract.get_tesseract_version()
            ocr_engines['tesseract'] = pytesseract
            print("✓ Tesseract引擎初始化成功")
        except Exception as e:
            print(f"✗ Tesseract引擎初始化失败: {e}")

    print(f"📊 已初始化 {len(ocr_engines)} 个OCR引擎: {list(ocr_engines.keys())}")
    return ocr_engines




class MultiTaskModel(nn.Module):
    """
    多任务模型：严格按照架构图实现
    架构：输入图像 -> Backbone -> Neck -> 双分支输出（元器件检测 + 文字检测识别）
    """
    def __init__(self, yolo_model_path: str, num_classes: int = 47, ocr_vocab_size: int = 6000):
        super(MultiTaskModel, self).__init__()

        print(f"🏗️ 初始化多任务模型架构...")
        print(f"   📊 元器件类别数: {num_classes}")
        print(f"   📝 OCR词汇量: {ocr_vocab_size}")

        # 保存模型路径
        self.yolo_model_path = yolo_model_path
        self.num_classes = num_classes
        self.ocr_vocab_size = ocr_vocab_size

        # 加载预训练YOLO模型并提取组件
        self.yolo_model = YOLO(yolo_model_path)
        self._extract_backbone_neck()
        self._build_detection_heads()

        # 初始化外部OCR引擎（用于对比和验证）
        self.init_ocr_engines()

        # 置信度阈值
        self.detection_conf_threshold = 0.25
        self.ocr_confidence_threshold = 0.5
        self.text_detection_threshold = 0.3

        print(f"✅ 多任务模型架构初始化完成")
        print(f"   🎯 检测阈值: {self.detection_conf_threshold}")
        print(f"   📝 OCR阈值: {self.ocr_confidence_threshold}")
        print(f"   🔍 文字检测阈值: {self.text_detection_threshold}")



    def init_ocr_engines(self):
        """初始化多个OCR引擎以提高识别精度"""
        self.ocr_engines = init_ocr_engines()

    def save_model_config(self, save_path):
        """保存模型配置到JSON文件"""
        import json
        import os

        # 创建保存目录
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        config = {
            'model_type': 'MultiTaskWrapper',
            'yolo_model_path': getattr(self, 'yolo_model_path', 'yolo11s.pt'),
            'num_classes': self.num_classes,
            'detection_conf_threshold': self.detection_conf_threshold,
            'ocr_confidence_threshold': self.ocr_confidence_threshold,
            'available_ocr_engines': list(self.ocr_engines.keys()) if hasattr(self, 'ocr_engines') else [],
            'version': '1.0',
            'created_time': str(time.time())
        }

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print(f"✅ 模型配置已保存到: {save_path}")

    def save_model(self, save_path):
        """保存完整模型（包括权重）到.pt文件"""
        import os

        # 创建保存目录
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 保存模型状态字典
        model_state = {
            'model_state_dict': self.state_dict(),
            'model_config': {
                'num_classes': self.num_classes,
                'ocr_vocab_size': getattr(self, 'ocr_vocab_size', 6000),
                'yolo_model_path': getattr(self, 'yolo_model_path', 'yolo11s.pt')
            },
            'version': '1.0',
            'created_time': str(time.time())
        }

        torch.save(model_state, save_path)
        print(f"✅ 模型权重已保存到: {save_path}")

    def _extract_backbone_neck(self):
        """
        从YOLO模型中提取Backbone和Neck组件
        严格按照架构图：输入 -> Backbone -> Neck -> 双分支输出
        """
        print("🔧 提取YOLO模型的Backbone和Neck组件...")

        # 获取YOLO模型的内部结构
        yolo_detection_model = self.yolo_model.model  # DetectionModel

        # 提取backbone层（通常是前10层）
        self.backbone_layers = nn.ModuleList()
        for i in range(10):  # YOLO11的backbone通常是前10层
            if i < len(yolo_detection_model.model):
                self.backbone_layers.append(yolo_detection_model.model[i])

        # 提取neck层（特征金字塔网络，通常是第10-22层）
        self.neck_layers = nn.ModuleList()
        for i in range(10, 22):
            if i < len(yolo_detection_model.model):
                self.neck_layers.append(yolo_detection_model.model[i])

        print(f"   ✅ Backbone层数: {len(self.backbone_layers)}")
        print(f"   ✅ Neck层数: {len(self.neck_layers)}")

    def _build_detection_heads(self):
        """
        构建双分支检测头：元器件检测头 + 文字检测识别头
        """
        print("🔧 构建双分支检测头...")

        # 1. 元器件检测头（创建自定义检测头，避免通道数不匹配）
        # 不使用YOLO原始检测头，因为会有通道数不匹配问题
        self.component_detection_head = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),  # 假设输入是256通道
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, self.num_classes + 5, 1)  # 输出：类别 + bbox(4) + 置信度(1)
        )

        # 2. 文字检测+识别头（新建）
        self.text_detection_head = self._build_text_detection_head()
        self.text_recognition_head = self._build_text_recognition_head()

        print("   ✅ 元器件检测头构建完成")
        print("   ✅ 文字检测头构建完成")
        print("   ✅ 文字识别头构建完成")

    def _build_text_detection_head(self):
        """构建文字检测头"""
        # 使用自适应的输入通道数
        return nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),  # 假设输入是256通道
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 1),  # 文字/背景二分类
            nn.Sigmoid()
        )

    def _build_text_recognition_head(self):
        """构建文字识别头"""
        return nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 25)),  # 池化到固定尺寸
            nn.Flatten(),
            nn.Linear(256 * 25, 512),  # 假设输入是256通道
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, self.ocr_vocab_size)  # 输出词汇表大小
        )

    def forward(self, x):
        """
        前向传播：严格按照架构图
        输入图像 -> Backbone -> Neck -> 双分支输出
        """
        # 使用YOLO模型进行特征提取
        # 获取中间特征而不是最终检测结果
        try:
            # 方法1：尝试使用YOLO的predict方法获取特征
            with torch.no_grad():
                # 直接使用YOLO模型的前向传播，但截取中间特征
                yolo_model = self.yolo_model.model

                # 逐层前向传播到neck层
                features = x
                for i, layer in enumerate(yolo_model.model):
                    features = layer(features)
                    # 在neck层之后停止（通常是第15-20层之间）
                    if i >= 15:  # 调整这个数字以获取合适的特征
                        break

                neck_features = features

        except Exception as e:
            print(f"⚠️ YOLO特征提取错误: {e}")
            # 创建一个简单的特征提取器作为备用
            batch_size = x.shape[0]
            neck_features = torch.randn(batch_size, 256, 20, 20)

        # 动态调整检测头的输入通道数
        if not hasattr(self, '_heads_initialized'):
            self._initialize_heads_with_features(neck_features)

        # 3. 双分支输出
        # 分支1：元器件检测（YOLO head）
        try:
            component_detections = self.component_detection_head(neck_features)
        except Exception as e:
            print(f"⚠️ 元器件检测头错误: {e}")
            batch_size = neck_features.shape[0]
            h, w = neck_features.shape[2], neck_features.shape[3]
            component_detections = torch.zeros(batch_size, self.num_classes + 5, h, w)

        # 分支2：文字检测+识别（OCR head）
        try:
            text_detection_map = self.text_detection_head(neck_features)
            text_recognition_features = self.text_recognition_head(neck_features)
        except Exception as e:
            print(f"⚠️ OCR检测头错误: {e}")
            batch_size = neck_features.shape[0]
            h, w = neck_features.shape[2], neck_features.shape[3]
            text_detection_map = torch.zeros(batch_size, 1, h, w)
            text_recognition_features = torch.zeros(batch_size, self.ocr_vocab_size)

        return {
            'component_detections': component_detections,
            'text_detection_map': text_detection_map,
            'text_recognition_features': text_recognition_features
        }

    def _initialize_heads_with_features(self, features):
        """根据实际特征维度初始化检测头"""
        in_channels = features.shape[1]
        print(f"🔧 根据特征维度初始化检测头，输入通道数: {in_channels}")

        # 重新构建文字检测头
        self.text_detection_head = nn.Sequential(
            nn.Conv2d(in_channels, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 1),
            nn.Sigmoid()
        )

        # 重新构建文字识别头
        self.text_recognition_head = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 25)),
            nn.Flatten(),
            nn.Linear(in_channels * 25, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, self.ocr_vocab_size)
        )

        self._heads_initialized = True

    def predict_with_architecture(self, image_path, save_result=True, output_dir='results'):
        """
        使用严格架构进行预测
        """
        print(f"🎯 使用架构模型进行预测: {image_path}")

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        # 预处理图像
        input_tensor = self._preprocess_image(image)

        # 前向传播
        with torch.no_grad():
            outputs = self.forward(input_tensor)

        # 后处理
        results = self._postprocess_outputs(outputs, image)

        # 添加基本信息
        results.update({
            'image_path': image_path,
            'timestamp': time.time(),
            'architecture': 'new_integrated'
        })

        # 保存结果
        if save_result:
            self.save_prediction_result(results, image, output_dir)

        return results

    def _preprocess_image(self, image):
        """预处理图像为模型输入"""
        # 调整大小到640x640
        resized = cv2.resize(image, (640, 640))

        # 转换为tensor
        tensor = torch.from_numpy(resized).permute(2, 0, 1).float() / 255.0
        tensor = tensor.unsqueeze(0)  # 添加batch维度

        return tensor

    def _postprocess_outputs(self, outputs, original_image):
        """后处理模型输出"""
        results = {
            'detections': [],  # 兼容原有格式
            'component_detections': [],
            'text_detections': [],
            'text_recognition': []
        }

        # 处理元器件检测结果
        component_dets = outputs['component_detections']
        # 简单的后处理：将张量转换为检测结果格式
        if component_dets is not None and component_dets.numel() > 0:
            # 创建一个简单的检测结果对象
            class SimpleDetectionResult:
                def __init__(self, tensor):
                    self.boxes = None  # 暂时为空，避免错误
                    self.conf = None
                    self.cls = None
                    self._tensor = tensor

            detection_result = SimpleDetectionResult(component_dets)
            results['detections'] = [detection_result]
            results['component_detections'] = [detection_result]

        # 处理文字检测结果
        text_det_map = outputs['text_detection_map']
        if text_det_map is not None and text_det_map.numel() > 0:
            # 简单处理：找到置信度高的区域
            threshold = 0.5
            text_regions = (text_det_map > threshold).nonzero()
            results['text_detections'] = text_regions.tolist() if len(text_regions) > 0 else []

        # 处理文字识别结果
        text_rec_features = outputs['text_recognition_features']
        if text_rec_features is not None and text_rec_features.numel() > 0:
            # 简单处理：取最高置信度的字符
            top_chars = torch.topk(text_rec_features, k=5, dim=-1)
            results['text_recognition'] = [{
                'text': f'char_{i}',
                'confidence': 0.8,
                'engine': 'neural_network'
            } for i in range(min(3, len(top_chars.indices[0])))]

        return results



    def detect_objects(self, image_path):
        """
        简化的YOLO目标检测（新架构使用）
        """
        print(f"🎯 YOLO目标检测: {image_path}")

        results = self.yolo_model.predict(
            image_path,
            conf=self.detection_conf_threshold,
            device='0' if torch.cuda.is_available() else 'cpu',
            verbose=False
        )

        return results









    def simple_ocr_recognition(self, image_region):
        """
        简化的OCR识别（新架构使用）
        仅使用最佳OCR引擎进行快速识别
        """
        if 'cnocr' in self.ocr_engines:
            try:
                from PIL import Image
                # 转换为PIL图像
                if len(image_region.shape) == 3:
                    image_pil = Image.fromarray(cv2.cvtColor(image_region, cv2.COLOR_BGR2RGB))
                else:
                    image_pil = Image.fromarray(image_region)

                results = self.ocr_engines['cnocr'].ocr(image_pil)
                if results:
                    return results[0].get('text', '') if results[0] else ''
            except Exception as e:
                print(f"OCR识别错误: {e}")

        return ''













    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        新架构预测：严格按照架构图实现
        输入图像 -> Backbone -> Neck -> 双分支输出（元器件检测 + 文字检测识别）
        """
        print(f"🎯 使用新架构进行预测: {image_path}")

        # 使用新架构进行预测
        return self.predict_with_architecture(image_path, save_result, output_dir)

    def draw_chinese_text(self, img, text, position, font_size=20, color=(255, 0, 0)):
        """使用PIL在图像上绘制中文文字"""
        try:
            # 将OpenCV图像转换为PIL图像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)

            # 尝试使用系统中文字体
            try:
                # Windows系统字体
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
            except:
                try:
                    # 备用字体
                    font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttf", font_size)
                except:
                    try:
                        # 再备用字体
                        font = ImageFont.truetype("arial.ttf", font_size)
                    except:
                        # 使用默认字体
                        font = ImageFont.load_default()

            # 绘制文字
            draw.text(position, text, font=font, fill=color)

            # 转换回OpenCV格式
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"绘制中文文字失败: {e}")
            # 如果失败，使用OpenCV默认方式（可能显示为???）
            cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            return img

    def save_prediction_result(self, result, image, output_dir):
        """
        保存预测结果（可视化图像和JSON数据）
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件名
        image_name = Path(result['image_path']).stem

        # 在图像上绘制结果
        result_image = image.copy()

        # 绘制目标检测框 - 绿色框住元器件
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                boxes = detection_result.boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 绘制元器件检测框 - 绿色 (BGR格式: 0,255,0)
                    cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                               (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果 - 蓝色框住文字
        for text_result in result['text_recognition']:
            # 兼容新的结果格式
            if 'detection_bbox' in text_result:
                bbox = text_result['detection_bbox']
            else:
                # 如果没有bbox信息，创建一个默认的
                bbox = [10, 10, 100, 30]

            text = text_result.get('text', 'unknown')
            confidence = text_result.get('confidence', 0.0)

            # 绘制文字区域 - 蓝色 (BGR格式: 255,0,0)
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

            # 使用支持中文的方式添加识别的文字 - 蓝色
            text_to_show = f'{text} ({confidence:.2f})'
            result_image = self.draw_chinese_text(result_image, text_to_show,
                                                (bbox[0], bbox[3]+20), font_size=16, color=(255, 0, 0))

        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        detections_list = []
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                detections_list = [
                    {
                        'bbox': box.xyxy[0].cpu().numpy().tolist(),
                        'confidence': float(box.conf[0]),
                        'class_id': int(box.cls[0])
                    } for box in detection_result.boxes
                ]

        json_result = {
            'image_path': result['image_path'],
            'detections': detections_list,
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2, default=self._json_serializer)

    def _json_serializer(self, obj):
        """
        JSON序列化辅助函数，处理numpy类型
        """
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'item'):  # torch tensor
            return obj.item()
        elif hasattr(obj, 'tolist'):  # torch tensor
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def train_yolo_model():
    """
    训练YOLO目标检测模型
    """
    print("🚀 开始训练YOLO目标检测模型...")

    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")

    # 加载预训练模型
    model = YOLO(r'yolo11s.pt')

    # 高精度训练参数配置
    training_args = {
        'data': r'yqjdataset/data.yaml',
        'epochs': 10,  # 增加训练轮数以提高精度
        'imgsz': 640,
        'batch': 16,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,  # 最终学习率
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'close_mosaic': 15,
        'workers': 4,
        'amp': True,  # 自动混合精度
        'single_cls': False,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
        'save': True,
        'save_period': 10,  # 每10个epoch保存一次
        'val': True,
        'plots': True,
        'verbose': True,
        # 数据增强参数 - 提高模型泛化能力
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results


















def create_multitask_model(yolo_model_path=None):
    """
    创建多任务模型 (YOLO目标检测 + CnOCR文字识别)
    """
    print("🚀 创建多任务模型...")

    # 创建多任务模型
    if yolo_model_path is None:
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    model = MultiTaskModel(yolo_model_path)

    print("✅ 多任务模型创建完成!")
    print("📝 使用YOLO进行目标检测 + CnOCR进行文字识别")

    return model


def create_integrated_model(yolo_model_path=None, save_path=None):
    """
    创建整合的YOLO+OCR模型并保存配置文件
    这个模型可以同时进行YOLO目标检测（绿色框）和OCR文字识别（蓝色框）
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    print(f"🔧 创建整合的YOLO+OCR模型，使用YOLO权重: {yolo_model_path}")
    print("📝 这是一个多任务模型，保存为配置文件")

    # 创建多任务模型
    integrated_model = MultiTaskModel(yolo_model_path)

    # 保存模型配置和权重
    if save_path is None:
        save_path = 'models/integrated_yolo_ocr_model.pt'

    # 确保路径以.pt结尾
    if not save_path.endswith('.pt'):
        save_path = save_path.replace('.json', '.pt')

    # 保存模型权重（.pt文件）
    integrated_model.save_model(save_path)

    # 同时保存配置文件（.json文件）
    config_path = save_path.replace('.pt', '.json')
    integrated_model.save_model_config(config_path)

    print("✅ 整合的YOLO+OCR模型创建并保存完成!")
    print(f"📁 模型权重保存到: {save_path}")
    print(f"📁 模型配置保存到: {config_path}")
    print("📝 使用说明:")
    print("   - 这是一个多任务模型，同时保存了权重(.pt)和配置(.json)")
    print("   - 可以同时进行YOLO目标检测（绿色框）和OCR文字识别（蓝色框）")
    print("   - 推理时可以加载.pt文件或.json配置文件")
    print("   - 支持CnOCR、EasyOCR等多个OCR引擎")

    return integrated_model, save_path

def load_integrated_model(model_path: str):
    """
    加载整合的YOLO+OCR模型
    """
    print(f"📂 加载整合模型配置: {model_path}")

    try:
        # 检查是否是JSON配置文件
        if model_path.endswith('.json'):
            import json
            with open(model_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('model_type') == 'MultiTaskWrapper':
                print("🔧 加载多任务包装模型...")
                yolo_model_path = config['yolo_model_path']

                # 创建多任务模型
                model = MultiTaskModel(yolo_model_path)

                # 恢复配置
                model.detection_conf_threshold = config.get('detection_conf_threshold', 0.15)
                model.ocr_confidence_threshold = config.get('ocr_confidence_threshold', 0.05)

                print(f"✅ 多任务包装模型加载完成")
                print(f"   🎯 YOLO模型: {yolo_model_path}")
                print(f"   📝 OCR引擎: {config.get('available_ocr_engines', [])}")

                return model
            else:
                print(f"❌ 不支持的模型类型: {config.get('model_type')}")
                return None
        elif model_path.endswith('.pt'):
            # 加载.pt文件
            print("🔧 加载.pt模型文件...")
            try:
                checkpoint = torch.load(model_path, map_location='cpu')

                if 'model_config' in checkpoint:
                    # 这是我们保存的整合模型
                    config = checkpoint['model_config']
                    yolo_model_path = config.get('yolo_model_path', 'yolo11s.pt')

                    # 创建模型
                    model = MultiTaskModel(
                        yolo_model_path=yolo_model_path,
                        num_classes=config.get('num_classes', 47),
                        ocr_vocab_size=config.get('ocr_vocab_size', 6000)
                    )

                    # 加载权重
                    model.load_state_dict(checkpoint['model_state_dict'])

                    print(f"✅ 整合模型加载完成")
                    print(f"   🎯 YOLO模型: {yolo_model_path}")
                    print(f"   📊 类别数: {config.get('num_classes', 47)}")

                    return model
                else:
                    print("❌ 不是有效的整合模型.pt文件")
                    return None

            except Exception as e:
                print(f"❌ 加载.pt文件失败: {e}")
                return None
        else:
            print("❌ 不支持的模型文件格式，请使用.pt或.json文件")
            return None

    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None




def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(tqdm(test_images[:10], desc="测试进度")):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = 0
            if len(result['detections']) > 0:
                detection_result = result['detections'][0]
                if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                    detection_count = len(detection_result.boxes)
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results


def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 高精度目标检测+OCR文字识别整合训练系统")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    try:
        _, _ = train_yolo_model()
        print("✅ YOLO模型训练完成")
    except Exception as e:
        print(f"⚠️ YOLO训练出错，使用预训练模型: {e}")

    # 步骤2: 创建多任务整合模型
    print("\n📍 步骤2: 创建多任务整合模型 (目标检测+OCR)")
    integrated_model, save_path = create_integrated_model()
    print(f"📁 整合模型配置已保存到: {save_path}")

    # 步骤3: 测试整合模型
    print("\n📍 步骤3: 测试整合模型性能")
    try:
        _ = test_integrated_model(integrated_model)
        print("✅ 模型测试完成")
    except Exception as e:
        print(f"⚠️ 模型测试出错: {e}")

    print("\n🎉 训练和测试完成!")
    print("📁 检查以下目录获取结果:")
    print("   - high_precision_detection/yolo11s_ocr_integrated/ (YOLO训练结果)")
    print("   - integrated_results/ (整合模型测试结果)")
    print("\n🔧 模型特点:")
    print("   ✓ 共享主干网络，提高计算效率")
    print("   ✓ 双分支输出：目标检测 + 文字识别")
    print("   ✓ 多OCR引擎融合，提高文字识别精度")
    print("   ✓ 端到端训练能力 (需要相应标注数据)")

    return integrated_model


def demo_multitask_prediction(image_path='DaYuanTuZ_0.png'):
    """
    演示多任务模型的预测功能
    """
    print(f"🎬 演示多任务模型预测功能")
    print(f"📸 测试图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    # 创建模型
    model, _ = create_integrated_model()

    # 进行预测
    try:
        result = model.predict(image_path, save_result=True, output_dir='demo_results')

        # 打印结果摘要
        detection_count = 0
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                detection_count = len(detection_result.boxes)
        text_count = len(result['text_recognition'])
        cnocr_text_regions = result.get('cnocr_text_regions', 0)
        detection_text_regions = result.get('detection_text_regions', 0)

        print(f"\n📊 预测结果摘要:")
        print(f"   🎯 检测到元器件: {detection_count} 个")
        print(f"   📝 识别到文字: {text_count} 段")
        print(f"   🧠 CnOCR文字区域: {cnocr_text_regions} 个")
        print(f"   🔍 检测框文字区域: {detection_text_regions} 个")

        if text_count > 0:
            print(f"\n📝 识别到的文字内容:")
            for i, text_result in enumerate(result['text_recognition'][:5]):  # 显示前5个
                print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

        print(f"\n💾 结果已保存到: demo_results/")

    except Exception as e:
        import traceback
        print(f"❌ 预测过程出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='YOLO+OCR整合模型训练和预测')
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'predict', 'create_integrated', 'demo'],
                       help='运行模式: train(训练), predict(预测), create_integrated(创建整合模型), demo(演示)')
    parser.add_argument('--image', type=str, default='DaYuanTuZ_0.png',
                       help='预测时使用的图像路径')
    parser.add_argument('--yolo_model', type=str, default=None,
                       help='YOLO模型路径')
    parser.add_argument('--save_path', type=str, default='models/integrated_yolo_ocr_model.pt',
                       help='整合模型保存路径')
    parser.add_argument('--load_model', type=str, default=None,
                       help='加载已保存的整合模型路径')

    args = parser.parse_args()

    if args.mode == 'train':
        # 完整训练模式
        print("🚀 开始完整训练流程...")
        model = main()

    elif args.mode == 'create_integrated':
        # 创建整合模型模式
        print("🔧 创建整合YOLO+OCR模型...")
        integrated_model, save_path = create_integrated_model(
            yolo_model_path=args.yolo_model,
            save_path=args.save_path
        )
        print(f"✅ 整合模型已创建并保存到: {save_path}")
        print("📝 现在您可以使用以下命令进行预测:")
        print(f"   python train.py --mode predict --load_model {save_path} --image your_image.png")

    elif args.mode == 'predict':
        # 预测模式
        print(f"🎯 预测模式，图像: {args.image}")

        if args.load_model:
            # 使用保存的整合模型
            print(f"📂 加载整合模型: {args.load_model}")
            model = load_integrated_model(args.load_model)
        else:
            # 使用多任务模型
            print("🔧 创建多任务模型...")
            model = create_multitask_model(args.yolo_model)

        # 进行预测
        if not os.path.exists(args.image):
            print(f"❌ 图像文件不存在: {args.image}")
        else:
            try:
                result = model.predict(args.image, save_result=True, output_dir='demo_results')

                # 打印结果摘要
                detection_count = 0
                if len(result['detections']) > 0:
                    detection_result = result['detections'][0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        detection_count = len(detection_result.boxes)
                text_count = len(result['text_recognition'])

                print(f"\n📊 预测结果摘要:")
                print(f"   🎯 检测到元器件: {detection_count} 个")
                print(f"   📝 识别到文字: {text_count} 段")

                if text_count > 0:
                    print(f"\n📝 识别到的文字内容:")
                    for i, text_result in enumerate(result['text_recognition'][:5]):
                        print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

                print(f"\n💾 结果已保存到: demo_results/")

            except Exception as e:
                import traceback
                print(f"❌ 预测过程出错: {e}")
                print(f"错误详情: {traceback.format_exc()}")

    elif args.mode == 'demo':
        # 演示模式
        demo_multitask_prediction(args.image)

    else:
        print(f"❌ 未知模式: {args.mode}")
        parser.print_help()