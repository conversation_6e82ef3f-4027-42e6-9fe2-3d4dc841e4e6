# 多任务模型架构（严格按照架构图）

## 架构图

```
输入电路图（RGB图像）
↓
主干网络 Backbone
↓
特征金字塔 Neck
↓
├── 元器件检测 head（YOLO head）→ 预测元器件类别和框
└── 文字检测 + 识别 head（OCR head）→ 预测文字框和字符内容
```

## 主要修改

### 1. 删除了冗余代码
- 删除了复杂的传统OCR方法
- 删除了重复的predict方法
- 简化了代码结构，严格按照架构图实现

### 2. 重新设计的架构
- **输入**: 电路图（RGB图像）
- **Backbone**: 使用YOLO的主干网络
- **Neck**: 使用YOLO的特征金字塔网络
- **双分支输出**:
  - 元器件检测 head（YOLO head）
  - 文字检测 + 识别 head（OCR head）

### 3. 核心类和方法

#### MultiTaskModel类
```python
class MultiTaskModel(nn.Module):
    def __init__(self, yolo_model_path, num_classes=47, ocr_vocab_size=6000)
    def forward(self, x)  # 严格按照架构图的前向传播
    def predict(self, image_path)  # 主预测方法
    def train_model(self, train_data_path, epochs=10)  # 训练方法
```

#### 关键方法
- `_extract_backbone_neck()`: 提取YOLO的Backbone和Neck
- `_build_detection_heads()`: 构建两个检测头
- `predict_with_architecture()`: 使用架构进行预测

## 使用方法

### 1. 基本使用
```python
from train import MultiTaskModel

# 初始化模型
model = MultiTaskModel('yolo11n.pt')

# 进行预测
result = model.predict('test_image.jpg')

print(f"元器件检测: {len(result['detections'])} 个目标")
print(f"文字区域检测: {len(result['text_detection'])} 个区域")
print(f"文字识别: {result['text_recognition']['text']}")
```

### 2. 命令行使用
```bash
# 演示严格架构
python train.py demo_strict

# 传统使用方式
python train.py predict --image test_image.jpg --yolo-model yolo11n.pt
```

### 3. 训练模型
```python
# 训练多任务模型
model.train_model(
    train_data_path='path/to/training/data',
    epochs=10,
    batch_size=8,
    learning_rate=0.001
)
```

## 输出格式

预测结果包含三个主要部分：

```python
{
    'image_path': 'test_image.jpg',
    'detections': [  # 元器件检测结果
        {
            'bbox': [x1, y1, x2, y2],
            'confidence': 0.85,
            'class_id': 0,
            'type': 'yolo_detection'
        }
    ],
    'text_detection': [  # 文字区域检测结果
        {
            'bbox': [x1, y1, x2, y2],
            'confidence': 0.75,
            'type': 'neural_text_detection'
        }
    ],
    'text_recognition': {  # 文字识别结果
        'text': '识别的文字内容',
        'confidence': 0.80,
        'method': 'neural_recognition'
    },
    'model_type': 'MultiTaskArchitecture_StrictDiagram',
    'timestamp': 1234567890.0
}
```

## 架构特点

1. **严格按照架构图**: 代码结构完全符合提供的架构图
2. **先删后加**: 删除了原有的冗余代码，重新实现了简洁的架构
3. **双分支设计**: 清晰的元器件检测和文字检测+识别分支
4. **统一特征提取**: 共享Backbone和Neck，提高效率
5. **简化的接口**: 提供简洁易用的API

## 注意事项

1. 需要安装相关依赖：ultralytics, torch, opencv-python等
2. 首次运行会自动下载YOLO模型
3. OCR功能需要安装easyocr, cnocr, pytesseract
4. 训练功能需要准备相应的数据集

## 文件结构

- `train.py`: 主要的模型实现文件
- `README_STRICT_ARCHITECTURE.md`: 本说明文档
- `results/`: 预测结果保存目录
- `strict_architecture_results/`: 严格架构演示结果目录
